import 'dart:ui';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/core/utils/image_picker_utils.dart';
import 'package:room_eight/viewmodels/profile_bloc/profile_bloc.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';

class AddPersonalDetailScreen extends StatelessWidget {
  const AddPersonalDetailScreen({super.key});

  static Widget builder(BuildContext context) =>
      const AddPersonalDetailScreen();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Theme.of(context).customColors.fillColor,
          body: Form(
            key: state.addPersonalDetailFormKey,
            child: SingleChildScrollView(
              child: BackgroundImage(
                imagePath: Assets.images.pngs.other.pngAuthBg2.path,
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: MediaQuery.of(context).size.height,
                  ),
                  child: IntrinsicHeight(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildTopSection(context),
                        buildSizedBoxH(25.h),
                        _buildPersonalDetailForm(context, state),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 60.h,
        bottom: 16.h,
      ),
      child: _buildTopBar(context),
    );
  }

  Widget _buildTopBar(BuildContext context) {
    final customColors = Theme.of(context).customColors;

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        CustomGradientContainer(
          height: 36.w,
          width: 36.w,
          topColor: customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
          bottomColor: customColors.blackColor!.withAlpha((0.4 * 255).toInt()),
          fillColor: customColors.fillColor!.withAlpha(75),
          child: CustomImageView(
            imagePath: Assets.images.svgs.icons.icBackArrow.path,
          ),
        ),
      ],
    );
  }

  Widget _buildPersonalDetailForm(BuildContext context, ProfileState state) {
    return Flexible(
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 7, sigmaY: 7),
          child: Container(
            width: double.infinity,
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height * 0.6,
            ),
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).customColors.fillColor?.withValues(alpha: 0.8),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDragHandle(context),
                  buildSizedBoxH(24.h),
                  _buildImageUpload(context, state),
                  buildSizedBoxH(24.h),
                  _buildFullNameField(context, state),
                  buildSizedBoxH(16.h),
                  _buildDateOfBirthField(context, state),
                  buildSizedBoxH(16.h),
                  _buildContactNumberField(context, state),
                  buildSizedBoxH(24.h),
                  // const Spacer(),
                  _buildAboutField(context, state),
                  buildSizedBoxH(16.h),
                  _buildProfilePicturesUpload(context, state),
                  buildSizedBoxH(16.h),
                  _buildProfileButton(context, state),
                  buildSizedBoxH(16.h),
                  // _buildSignUpOption(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDragHandle(BuildContext context) {
    return Column(
      children: [
        Text(
          Lang.of(context).lbl_your_personal_details,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 22.sp,
            color: Theme.of(context).customColors.blackColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        buildSizedBoxH(4.h),
        Text(
          Lang.of(context).lbl_your_personal_details_desc,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 14.sp,
            color: Theme.of(context).customColors.darkGreytextcolor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildFullNameField(BuildContext context, ProfileState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_what_is_your_full_name,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        buildSizedBoxH(4.h),
        CustomTextInputField(
          context: context,
          type: InputType.text,
          hintLabel: Lang.of(context).lbl_full_name,
          controller: state.fullNameController,
          focusNode: state.fullNameFocusNode,
          textInputAction: TextInputAction.next,
          validator: (value) => AppValidations.nameValidation(value, context),
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(15.0.r),
            imagePath: Assets.images.svgs.icons.icProfile.path,
          ),
          onChanged: (value) =>
              context.read<ProfileBloc>().add(FullProfileNameChanged(value)),
        ),
      ],
    );
  }

  Widget _buildDateOfBirthField(BuildContext context, ProfileState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_what_is_your_date_of_birth,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        buildSizedBoxH(4.h),
        CustomTextInputField(
          context: context,
          type: InputType.text,
          readOnly: true,
          hintLabel: "DD/MM/YYYY",
          controller: state.dobController,
          focusNode: state.dobFocusNode,
          textInputAction: TextInputAction.next,
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(15.0.r),
            imagePath: Assets.images.svgs.icons.icCalender.path,
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Date of birth is required';
            }
            return null;
          },
          onTap: () async {
            FocusScope.of(context).requestFocus(FocusNode());
            final now = DateTime.now();
            final picked = await showDatePicker(
              context: context,
              initialDate: now.subtract(const Duration(days: 365 * 18)),
              firstDate: DateTime(1900),
              lastDate: now,
              builder: (context, child) {
                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: ColorScheme.light(
                      primary:
                          Theme.of(context).customColors.primaryColor ??
                          Theme.of(context).primaryColor,
                    ),
                  ),
                  child: child!,
                );
              },
            );
            Logger.lOG(picked);
            if (picked != null) {
              final formatted =
                  "${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}";
              // ignore: use_build_context_synchronously
              context.read<ProfileBloc>().add(
                ProfileDateOfBirthChanged(formatted),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildContactNumberField(BuildContext context, ProfileState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_what_is_your_contact_number,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        buildSizedBoxH(4.h),
        CustomTextInputField(
          context: context,
          type: InputType.phoneNumber,
          hintLabel: Lang.of(context).lbl_contact_number,
          controller: state.contactNumberController,
          focusNode: state.contactNumberFocusNode,
          textInputAction: TextInputAction.next,
          validator: (value) => AppValidations.phoneValidation(value, context),
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(15.0.r),
            imagePath: Assets.images.svgs.icons.icPhone.path,
          ),
          onChanged: (value) =>
              context.read<ProfileBloc>().add(ContactNumberChanged(value)),
        ),
      ],
    );
  }

  Widget _buildProfileButton(BuildContext context, ProfileState state) {
    return CustomElevatedButton(
      isLoading: state.isloginLoading,
      isDisabled: state.isloginLoading,
      text: Lang.of(context).lbl_continue,
      buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
        color: Theme.of(context).customColors.fillColor,
        fontSize: 18.0.sp,
        fontWeight: FontWeight.w500,
      ),
      onPressed: () {
        FocusManager.instance.primaryFocus?.unfocus();
        if (state.addPersonalDetailFormKey.currentState?.validate() ?? false) {
          context.read<ProfileBloc>().add(ProfileSubmitted());
        }
      },
    );
  }

  Widget _buildImageUpload(BuildContext context, ProfileState state) {
    return Column(
      children: [
        Text(
          Lang.of(context).lbl_add_your_image,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        buildSizedBoxH(4.h),
        Stack(
          children: [
            Container(
              height: 100.h,
              width: 100.h,
              decoration: BoxDecoration(
                color: Theme.of(context).customColors.fillColor,
                borderRadius: BorderRadius.circular(50.r),
              ),
              child:
                  (state.userProfile != null &&
                      state.userProfile!.path.isNotEmpty)
                  ? CustomImageView(
                      imagePath: state.userProfile?.path,
                      fit: BoxFit.cover,
                      radius: BorderRadius.circular(50.r),
                    )
                  : CustomImageView(
                      imagePath: Assets.images.svgs.icons.icPerson.path,
                      margin: EdgeInsets.all(25.r),
                      color: Theme.of(context).customColors.darkGreytextcolor,
                    ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: GestureDetector(
                onTap: () {
                  context.read<ProfileBloc>().add(SelectUserProfile());
                },
                child: CustomImageView(
                  imagePath: Assets.images.pngs.icons.icAdd.path,
                  height: 27.h,
                  width: 27.w,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _builsLabel(BuildContext context, String label) {
    return Text(
      label,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(
        fontSize: 16.sp,
        color: Theme.of(context).customColors.blackColor,
      ),
    );
  }

  Widget _buildAboutField(BuildContext context, ProfileState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _builsLabel(context, 'About You'),
        buildSizedBoxH(10.h),
        TextFormField(
          controller: state.aboutController,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'About is required';
            }
            return null;
          },
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Tell us about yourself',
            hintStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontSize: 18.sp,
              color: Theme.of(context).customColors.hinttextcolor,
              fontWeight: FontWeight.w400,
            ),
            contentPadding: const EdgeInsets.all(16),
            fillColor: Theme.of(context).customColors.fillColor,
            filled: true,
            border: OutlineInputBorder(
),
        ),
        // CustomTextInputField(
        //   context: context,
        //   type: InputType.text,
        //   hintLabel: 'Tell us about yourself',
        //   controller: state.aboutController,
        //   focusNode: state.aboutFocusNode,
        //   textInputAction: TextInputAction.done,
        //   maxLines: 3,

        //   validator: (value) {
        //     if (value == null || value.isEmpty) {
        //       return 'About is required';
        //     }
        //     return null;
        //   },
        //   onChanged: (value) =>
        //       context.read<ProfileBloc>().add(AboutChanged(value)),
        // ),
      ],
    );
  }

  Widget _buildProfilePicturesUpload(BuildContext context, ProfileState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _builsLabel(context, 'Profile Pictures'),
        buildSizedBoxH(10.h),
        SizedBox(
          height: 90.h,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: state.photoPaths.length + 1,
            separatorBuilder: (_, __) => SizedBox(width: 8.w),
            padding: const EdgeInsets.only(right: 16.0),
            itemBuilder: (context, index) {
              if (index == 0) {
                // Add-photo button
                return Padding(
                  padding: const EdgeInsets.only(left: 16.0),
                  child: GestureDetector(
                    onTap: () async {
                      final images =
                          await Imagepickerutils.pickMultipleImagesFromGallery();
                      if (images.isNotEmpty && context.mounted) {
                        final imagePaths = images
                            .map((img) => img.path)
                            .toList();
                        context.read<ProfileBloc>().add(
                          SelectMultiplePhotos(imagePaths),
                        );
                      }
                    },
                    child: Container(
                      height: 90.h,
                      width: 80.w,
                      decoration: BoxDecoration(
                        color: Theme.of(context).customColors.fillColor,
                        borderRadius: BorderRadius.circular(16.r),
                        border: Border.all(
                          color: Theme.of(context).customColors.primaryColor!,
                          width: 1.5,
                        ),
                      ),
                      child: Icon(
                        Icons.add_a_photo,
                        size: 32,
                        color: Theme.of(context).customColors.primaryColor,
                      ),
                    ),
                  ),
                );
              } else {
                final imagePath = state.photoPaths[index - 1];
                return Stack(
                  children: [
                    CustomImageView(
                      height: 90.h,
                      width: 80.w,
                      imagePath: imagePath,
                      radius: BorderRadius.circular(12.r),
                    ),
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: () {
                          context.read<ProfileBloc>().add(
                            RemovePhoto(imagePath),
                          );
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).customColors.fillColor,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.close,
                            size: 18,
                            color: Theme.of(context).customColors.redcolor,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ),
      ],
    );
  }
}
