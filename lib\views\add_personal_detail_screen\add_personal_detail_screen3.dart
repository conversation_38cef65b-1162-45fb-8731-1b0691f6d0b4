import 'dart:ui';
import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/profile_model/profile_model.dart';
import 'package:room_eight/viewmodels/profile_bloc/profile_bloc.dart';
import 'package:room_eight/views/add_personal_detail_screen/widget/add_personal_detail_screen3_shimmer.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';

class AddPersonalDetailScreen3 extends StatelessWidget {
  const AddPersonalDetailScreen3({super.key});

  static Widget builder(BuildContext context) => AddPersonalDetailScreen3();

  // final List<Map<String, dynamic>> habitsLifestyle = [
  //   {'name': 'Night Owl', 'image': Assets.images.pngs.icons.icStar.path},
  //   {'name': 'Early Riser', 'image': Assets.images.pngs.icons.icSun.path},
  //   {'name': 'Occasionally', 'image': Assets.images.pngs.icons.icSmoke.path},
  //   {'name': 'Cats', 'image': Assets.images.pngs.icons.icCat.path},
  //   {'name': 'Coffee', 'image': Assets.images.pngs.icons.icCoffee.path},
  // ];

  // final List<Map<String, dynamic>> cleanlinessLivingStyle = [
  //   {'name': 'Clean Freak', 'image': Assets.images.pngs.icons.icClean.path},
  //   {'name': 'Chill', 'image': Assets.images.pngs.icons.icEmoj.path},
  //   {'name': 'Environment', 'image': Assets.images.pngs.icons.icEarth.path},
  //   {'name': 'Study Focused', 'image': Assets.images.pngs.icons.icLearn.path},
  //   {'name': 'Vegetarian', 'image': Assets.images.pngs.icons.icVage.path},
  // ];

  // final List<Map<String, dynamic>> iterestsHobbies = [
  //   {'name': 'Gaming', 'image': Assets.images.pngs.icons.icCansol.path},
  //   {'name': 'Gym Rat', 'image': Assets.images.pngs.icons.icGym.path},
  //   {'name': 'Art & Crafts', 'image': Assets.images.pngs.icons.icArt.path},
  //   {'name': 'Music Lover', 'image': Assets.images.pngs.icons.icSong.path},
  //   {'name': 'Dancing', 'image': Assets.images.pngs.icons.icDance.path},
  //   {'name': 'Camping', 'image': Assets.images.pngs.icons.icCampe.path},
  // ];

  @override
  Widget build(BuildContext context) {
    context.read<ProfileBloc>().add(LoadProfilePageData());
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, state) {
        if (state.profileLoading == true) {
          return AddPersonalDetailScreen3Shimmer();
        }
        return Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: Theme.of(context).customColors.fillColor,
          body: BackgroundImage(
            imagePath: Assets.images.pngs.other.pngAuthBg4.path,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height,
              ),
              child: IntrinsicHeight(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildTopSection(context),
                    buildSizedBoxH(25.h),
                    Expanded(child: _buildSelectionMenuLists(context, state)),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopSection(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        top: 60.h,
        bottom: 16.h,
      ),
      child: _buildTopBar(context),
    );
  }

  Widget _buildTopBar(BuildContext context) {
    final customColors = Theme.of(context).customColors;

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        CustomGradientContainer(
          height: 36.w,
          width: 36.w,
          topColor: customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
          bottomColor: customColors.blackColor!.withAlpha((0.4 * 255).toInt()),
          fillColor: customColors.fillColor!.withAlpha(75),
          child: CustomImageView(
            imagePath: Assets.images.svgs.icons.icBackArrow.path,
          ),
        ),
      ],
    );
  }

  Widget _buildSelectionMenuLists(BuildContext context, ProfileState state) {
    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 7, sigmaY: 7),
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height * 0.6,
          ),
          decoration: BoxDecoration(
            color: Theme.of(
              context,
            ).customColors.fillColor?.withValues(alpha: 0.8),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDragHandle(context),
                buildSizedBoxH(24.h),
                Expanded(child: _buildMenuDetails(context, state)),
                buildSizedBoxH(24.h),
                // const Spacer(),
                _buildProfileButton(context, state),
                // buildSizedBoxH(16.h),
                // _buildSignUpOption(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDragHandle(BuildContext context) {
    return Column(
      children: [
        Text(
          Lang.of(context).lbl_find_the_right_room8,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 22.sp,
            color: Theme.of(context).customColors.blackColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        buildSizedBoxH(4.h),
        Text(
          Lang.of(context).lbl_your_personal_details_desc3,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 14.sp,
            color: Theme.of(context).customColors.darkGreytextcolor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildMenuDetails(BuildContext context, ProfileState state) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAgreementCheckbox(context, state),
          buildSizedBoxH(12.h),
          CustomTextInputField(
            context: context,
            type: InputType.text,
            hintLabel: Lang.of(context).lbl_what_are_your_interests,
            controller: state.searchController,
            focusNode: state.fullNameFocusNode,
            textInputAction: TextInputAction.next,
            onChanged: (value) =>
                context.read<ProfileBloc>().add(SearchChanged(value)),
          ),
          buildSizedBoxH(12.h),
          _builsLabel(context, Lang.of(context).lbl_habits_lifestyle),
          buildSizedBoxH(10.h),
          _buildOptionsList(
            context: context,
            options: state.habitsLifestyle,
            selectedHabits: state.selectedHabitsAndLifestyle,
            onSelect: (name) {
              context.read<ProfileBloc>().add(
                SelectHabitsAndLifestyle(habitsAndLifestyle: name),
              );
            },
          ),
          buildSizedBoxH(12.h),
          _builsLabel(context, Lang.of(context).lbl_cleanliness_living_style),
          _buildOptionsList(
            context: context,
            options: state.livingStyle,
            selectedHabits: state.selectedCleanlinessLivingStyle,
            onSelect: (name) {
              context.read<ProfileBloc>().add(
                SelectCleanlinessLivingStyle(cleanlinessLivingStyle: name),
              );
            },
          ),
          buildSizedBoxH(12.h),
          _builsLabel(context, Lang.of(context).lbl_interests_hobbies),
          _buildOptionsList(
            context: context,
            options: state.interestsHobbies,
            selectedHabits: state.selectedInterestsHobbies,
            onSelect: (name) {
              context.read<ProfileBloc>().add(
                SelectInterestsHobbies(interestsHobbies: name),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOptionDesign(
    BuildContext context,
    String text,
    String image,
    bool isSelected,
    void Function()? onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Chip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomImageView(
              imagePath: ApiEndPoint.getSelectionMenuIconImageUrl + image,
              height: 20.h,
              width: 20.w,
            ),
            buildSizedboxW(5.w),
            Text(
              text,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: isSelected
                    ? Theme.of(context).customColors.fillColor
                    : Theme.of(context).customColors.darkGreytextcolor,
                fontSize: 16.0.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        backgroundColor: isSelected
            ? Theme.of(context).customColors.primaryColor
            : Theme.of(context).customColors.fillColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30.r), // Change radius as needed
          side: BorderSide(color: Colors.transparent, width: 0), // No border
        ),
      ),
    );
  }

  Widget _buildOptionsList({
    required BuildContext context,
    required List<ProfileOptionModel> options,
    required List<String> selectedHabits,
    required void Function(String name) onSelect,
  }) {
    return Wrap(
      children: List.generate(
        options.length,
        (index) => Padding(
          padding: EdgeInsets.only(right: 5.w),
          child: _buildOptionDesign(
            context,
            options[index].name ?? '',
            options[index].icon ?? '',
            selectedHabits.contains(options[index].name),
            () => onSelect(options[index].name ?? ''),
          ),
        ),
      ),
    );
  }

  Widget _buildAgreementCheckbox(BuildContext context, ProfileState state) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 20.h,
          child: Checkbox(
            value: state.isPickThings,
            onChanged: (bool? value) {
              context.read<ProfileBloc>().add(TogglePickThings());
            },
            activeColor: Theme.of(context).customColors.greencolor,
            checkColor: Theme.of(context).customColors.fillColor,
          ),
        ),
        buildSizedboxW(8.w),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                fontSize: 14.sp,
                color: Theme.of(context).customColors.darkGreytextcolor,
                height: 1.5,
              ),
              children: [
                TextSpan(
                  text: '${Lang.of(context).lbl_pick_5_things} ',
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).customColors.darkGreytextcolor,
                    fontSize: 14.sp,
                  ),
                ),
                TextSpan(
                  text: Lang.of(
                    context,
                  ).lbl_that_matter_to_you_when_living_with_a_room8,
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                    fontSize: 14.sp,
                    color: Theme.of(context).customColors.darkGreytextcolor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _builsLabel(BuildContext context, String label) {
    return Text(
      label,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(
        fontSize: 16.sp,
        color: Theme.of(context).customColors.blackColor,
      ),
    );
  }

  Widget _buildProfileButton(BuildContext context, ProfileState state) {
    return CustomElevatedButton(
      isLoading: state.isloginLoading,
      isDisabled: state.isloginLoading,
      text: " ${state.selectedOption}/5 ${Lang.of(context).lbl_selected}",
      buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
        color: Theme.of(context).customColors.fillColor,
        fontSize: 18.0.sp,
        fontWeight: FontWeight.w500,
      ),
      onPressed: () {
        FocusManager.instance.primaryFocus?.unfocus();
        if (state.selectedOption >= 5) {
          // Submit the final profile data to API
          context.read<ProfileBloc>().add(FinalProfileSubmitted());
        }
      },
    );
  }
}

// import 'dart:ui';
// import 'package:room_eight/core/utils/app_exports.dart';
// import 'package:room_eight/viewmodels/profile_bloc/profile_bloc.dart';
// import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';

// class AddPersonalDetailScreen3 extends StatelessWidget {
//   AddPersonalDetailScreen3({super.key});

//   static Widget builder(BuildContext context) => AddPersonalDetailScreen3();

//   final List<Map<String, dynamic>> habitsLifestyle = [
//     {'name': 'Night Owl', 'image': Assets.images.pngs.icons.icStar.path},
//     {'name': 'Early Riser', 'image': Assets.images.pngs.icons.icSun.path},
//     {'name': 'Occasionally', 'image': Assets.images.pngs.icons.icSmoke.path},
//     {'name': 'Cats', 'image': Assets.images.pngs.icons.icCat.path},
//     {'name': 'Coffee', 'image': Assets.images.pngs.icons.icCoffee.path},
//   ];

//   final List<Map<String, dynamic>> cleanlinessLivingStyle = [
//     {'name': 'Clean Freak', 'image': Assets.images.pngs.icons.icClean.path},
//     {'name': 'Chill', 'image': Assets.images.pngs.icons.icEmoj.path},
//     {'name': 'Environment', 'image': Assets.images.pngs.icons.icEarth.path},
//     {'name': 'Study Focused', 'image': Assets.images.pngs.icons.icLearn.path},
//     {'name': 'Vegetarian', 'image': Assets.images.pngs.icons.icVage.path},
//   ];

//   final List<Map<String, dynamic>> iterestsHobbies = [
//     {'name': 'Gaming', 'image': Assets.images.pngs.icons.icCansol.path},
//     {'name': 'Gym Rat', 'image': Assets.images.pngs.icons.icGym.path},
//     {'name': 'Art & Crafts', 'image': Assets.images.pngs.icons.icArt.path},
//     {'name': 'Music Lover', 'image': Assets.images.pngs.icons.icSong.path},
//     {'name': 'Dancing', 'image': Assets.images.pngs.icons.icDance.path},
//     {'name': 'Camping', 'image': Assets.images.pngs.icons.icCampe.path},
//   ];

//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<ProfileBloc, ProfileState>(
//       builder: (context, state) {
//         return Scaffold(
//           resizeToAvoidBottomInset: false,
//           backgroundColor: Theme.of(context).customColors.fillColor,
//           body: BackgroundImage(
//             imagePath: Assets.images.pngs.other.pngAuthBg4.path,
//             child: ConstrainedBox(
//               constraints: BoxConstraints(
//                 minHeight: MediaQuery.of(context).size.height,
//               ),
//               child: IntrinsicHeight(
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     _buildTopSection(context),
//                     buildSizedBoxH(25.h),
//                     Expanded(child: _buildLoginForm(context, state)),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }

//   Widget _buildTopSection(BuildContext context) {
//     return Padding(
//       padding: EdgeInsets.only(
//         left: 16.w,
//         right: 16.w,
//         top: 60.h,
//         bottom: 16.h,
//       ),
//       child: _buildTopBar(context),
//     );
//   }

//   Widget _buildTopBar(BuildContext context) {
//     final customColors = Theme.of(context).customColors;

//     return Row(
//       mainAxisAlignment: MainAxisAlignment.start,
//       children: [
//         CustomGradientContainer(
//           height: 36.w,
//           width: 36.w,
//           topColor: customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
//           bottomColor: customColors.blackColor!.withAlpha((0.4 * 255).toInt()),
//           fillColor: customColors.fillColor!.withAlpha(75),
//           child: CustomImageView(
//             imagePath: Assets.images.svgs.icons.icBackArrow.path,
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildLoginForm(BuildContext context, ProfileState state) {
//     return ClipRRect(
//       borderRadius: const BorderRadius.only(
//         topLeft: Radius.circular(20),
//         topRight: Radius.circular(20),
//       ),
//       child: BackdropFilter(
//         filter: ImageFilter.blur(sigmaX: 7, sigmaY: 7),
//         child: Container(
//           width: double.infinity,
//           constraints: BoxConstraints(
//             minHeight: MediaQuery.of(context).size.height * 0.6,
//           ),
//           decoration: BoxDecoration(
//             color: Theme.of(
//               context,
//             ).customColors.fillColor?.withValues(alpha: 0.8),
//             borderRadius: const BorderRadius.only(
//               topLeft: Radius.circular(20),
//               topRight: Radius.circular(20),
//             ),
//           ),
//           child: Padding(
//             padding: EdgeInsets.all(16.w),
//             child: Column(
//               mainAxisSize: MainAxisSize.min,
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 _buildDragHandle(context),
//                 buildSizedBoxH(24.h),
//                 Expanded(child: _buildDetails(context, state)),
//                 buildSizedBoxH(24.h),
//                 // const Spacer(),
//                 _buildProfileButton(context, state),
//                 // buildSizedBoxH(16.h),
//                 // _buildSignUpOption(context),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildDragHandle(BuildContext context) {
//     return Column(
//       children: [
//         Text(
//           Lang.of(context).lbl_find_the_right_room8,
//           textAlign: TextAlign.center,
//           style: Theme.of(context).textTheme.labelLarge?.copyWith(
//             fontSize: 22.sp,
//             color: Theme.of(context).customColors.blackColor,
//             fontWeight: FontWeight.bold,
//           ),
//         ),
//         buildSizedBoxH(4.h),
//         Text(
//           Lang.of(context).lbl_your_personal_details_desc3,
//           textAlign: TextAlign.center,
//           style: Theme.of(context).textTheme.labelLarge?.copyWith(
//             fontSize: 14.sp,
//             color: Theme.of(context).customColors.darkGreytextcolor,
//             fontWeight: FontWeight.w500,
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildDetails(BuildContext context, ProfileState state) {
//     return SingleChildScrollView(
//       physics: const BouncingScrollPhysics(),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           _buildAgreementCheckbox(context, state),
//           buildSizedBoxH(12.h),
//           CustomTextInputField(
//             context: context,
//             type: InputType.text,
//             hintLabel: Lang.of(context).lbl_what_are_your_interests,
//             controller: state.searchController,
//             focusNode: state.fullNameFocusNode,
//             textInputAction: TextInputAction.next,
//             onChanged: (value) =>
//                 context.read<ProfileBloc>().add(SearchChanged(value)),
//           ),
//           buildSizedBoxH(12.h),
//           _builsLabel(context, Lang.of(context).lbl_habits_lifestyle),
//           buildSizedBoxH(10.h),
//           _buildOptionsList(
//             context: context,
//             habitsLifestyle: habitsLifestyle,
//             selectedHabits: state.selectedHabitsAndLifestyle,
//             onSelect: (name) {
//               context.read<ProfileBloc>().add(
//                 SelectHabitsAndLifestyle(habitsAndLifestyle: name),
//               );
//             },
//           ),
//           buildSizedBoxH(12.h),
//           _builsLabel(context, Lang.of(context).lbl_cleanliness_living_style),
//           _buildOptionsList(
//             context: context,
//             habitsLifestyle: cleanlinessLivingStyle,
//             selectedHabits: state.selectedCleanlinessLivingStyle,
//             onSelect: (name) {
//               context.read<ProfileBloc>().add(
//                 SelectCleanlinessLivingStyle(cleanlinessLivingStyle: name),
//               );
//             },
//           ),
//           buildSizedBoxH(12.h),
//           _builsLabel(context, Lang.of(context).lbl_interests_hobbies),
//           _buildOptionsList(
//             context: context,
//             habitsLifestyle: iterestsHobbies,
//             selectedHabits: state.selectedInterestsHobbies,
//             onSelect: (name) {
//               context.read<ProfileBloc>().add(
//                 SelectInterestsHobbies(interestsHobbies: name),
//               );
//             },
//           ),
//           buildSizedBoxH(24.h),
//           // _buildAboutField(context, state),
//           // buildSizedBoxH(16.h),
//           // _buildProfilePicturesUpload(context, state),
//         ],
//       ),
//     );
//   }

//   Widget _buildOptionDesign(
//     BuildContext context,
//     String text,
//     String image,
//     bool isSelected,
//     void Function()? onTap,
//   ) {
//     return GestureDetector(
//       onTap: onTap,
//       child: Chip(
//         label: Row(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             CustomImageView(imagePath: image, height: 20.h, width: 20.w),
//             buildSizedboxW(5.w),
//             Text(
//               text,
//               style: Theme.of(context).textTheme.bodyLarge?.copyWith(
//                 color: isSelected
//                     ? Theme.of(context).customColors.fillColor
//                     : Theme.of(context).customColors.darkGreytextcolor,
//                 fontSize: 16.0.sp,
//                 fontWeight: FontWeight.w500,
//               ),
//             ),
//           ],
//         ),
//         backgroundColor: isSelected
//             ? Theme.of(context).customColors.primaryColor
//             : Theme.of(context).customColors.fillColor,
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(30.r), // Change radius as needed
//           side: BorderSide(color: Colors.transparent, width: 0), // No border
//         ),
//       ),
//     );
//   }

//   Widget _buildOptionsList({
//     required BuildContext context,
//     required List<Map<String, dynamic>> habitsLifestyle,
//     required List<String> selectedHabits,
//     required void Function(String name) onSelect,
//   }) {
//     return Wrap(
//       children: List.generate(
//         habitsLifestyle.length,
//         (index) => Padding(
//           padding: EdgeInsets.only(right: 5.w),
//           child: _buildOptionDesign(
//             context,
//             habitsLifestyle[index]['name'],
//             habitsLifestyle[index]['image'],
//             selectedHabits.contains(habitsLifestyle[index]['name']),
//             () => onSelect(habitsLifestyle[index]['name']),
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildAgreementCheckbox(BuildContext context, ProfileState state) {
//     return Row(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         SizedBox(
//           height: 20.h,
//           child: Checkbox(
//             value: state.isPickThings,
//             onChanged: (bool? value) {
//               context.read<ProfileBloc>().add(TogglePickThings());
//             },
//             activeColor: Theme.of(context).customColors.greencolor,
//             checkColor: Theme.of(context).customColors.fillColor,
//           ),
//         ),
//         buildSizedboxW(8.w),
//         Expanded(
//           child: RichText(
//             text: TextSpan(
//               style: Theme.of(context).textTheme.bodyLarge!.copyWith(
//                 fontSize: 14.sp,
//                 color: Theme.of(context).customColors.darkGreytextcolor,
//                 height: 1.5,
//               ),
//               children: [
//                 TextSpan(
//                   text: '${Lang.of(context).lbl_pick_5_things} ',
//                   style: Theme.of(context).textTheme.bodyLarge!.copyWith(
//                     fontWeight: FontWeight.bold,
//                     color: Theme.of(context).customColors.darkGreytextcolor,
//                     fontSize: 14.sp,
//                   ),
//                 ),
//                 TextSpan(
//                   text: Lang.of(
//                     context,
//                   ).lbl_that_matter_to_you_when_living_with_a_room8,
//                   style: Theme.of(context).textTheme.bodyLarge!.copyWith(
//                     fontSize: 14.sp,
//                     color: Theme.of(context).customColors.darkGreytextcolor,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _builsLabel(BuildContext context, String label) {
//     return Text(
//       label,
//       style: Theme.of(context).textTheme.labelLarge?.copyWith(
//         fontSize: 16.sp,
//         color: Theme.of(context).customColors.blackColor,
//       ),
//     );
//   }

//   Widget _buildProfileButton(BuildContext context, ProfileState state) {
//     return CustomElevatedButton(
//       isLoading: state.isloginLoading,
//       isDisabled: state.isloginLoading,
//       text: " ${state.selectedOption}/5 ${Lang.of(context).lbl_selected}",
//       buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
//         color: Theme.of(context).customColors.fillColor,
//         fontSize: 18.0.sp,
//         fontWeight: FontWeight.w500,
//       ),
//       onPressed: () {
//         FocusManager.instance.primaryFocus?.unfocus();
//         if (state.selectedOption >= 5) {
//           // Submit the final profile data to API
//           context.read<ProfileBloc>().add(FinalProfileSubmitted());
//         }
//       },
//     );
//   }

//   // Widget _buildAboutField(BuildContext context, ProfileState state) {
//   //   return Column(
//   //     crossAxisAlignment: CrossAxisAlignment.start,
//   //     children: [
//   //       _builsLabel(context, 'About You'),
//   //       buildSizedBoxH(10.h),
//   //       CustomTextInputField(
//   //         context: context,
//   //         type: InputType.text,
//   //         hintLabel: 'Tell us about yourself',
//   //         controller: state.aboutController,
//   //         focusNode: state.aboutFocusNode,
//   //         textInputAction: TextInputAction.done,
//   //         maxLines: 3,
//   //         validator: (value) {
//   //           if (value == null || value.isEmpty) {
//   //             return 'About is required';
//   //           }
//   //           return null;
//   //         },
//   //         onChanged: (value) =>
//   //             context.read<ProfileBloc>().add(AboutChanged(value)),
//   //       ),
//   //     ],
//   //   );
//   // }

//   // Widget _buildProfilePicturesUpload(BuildContext context, ProfileState state) {
//   //   return Column(
//   //     crossAxisAlignment: CrossAxisAlignment.start,
//   //     children: [
//   //       _builsLabel(context, 'Profile Pictures'),
//   //       buildSizedBoxH(10.h),
//   //       SizedBox(
//   //         height: 90.h,
//   //         child: ListView.separated(
//   //           scrollDirection: Axis.horizontal,
//   //           itemCount: state.photoPaths.length + 1,
//   //           separatorBuilder: (_, __) => SizedBox(width: 8.w),
//   //           padding: const EdgeInsets.only(right: 16.0),
//   //           itemBuilder: (context, index) {
//   //             if (index == 0) {
//   //               // Add-photo button
//   //               return Padding(
//   //                 padding: const EdgeInsets.only(left: 16.0),
//   //                 child: GestureDetector(
//   //                   onTap: () async {
//   //                     final images =
//   //                         await Imagepickerutils.pickMultipleImagesFromGallery();
//   //                     if (images.isNotEmpty && context.mounted) {
//   //                       final imagePaths = images
//   //                           .map((img) => img.path)
//   //                           .toList();
//   //                       context.read<ProfileBloc>().add(
//   //                         SelectMultiplePhotos(imagePaths),
//   //                       );
//   //                     }
//   //                   },
//   //                   child: Container(
//   //                     height: 90.h,
//   //                     width: 80.w,
//   //                     decoration: BoxDecoration(
//   //                       color: Theme.of(context).customColors.fillColor,
//   //                       borderRadius: BorderRadius.circular(16.r),
//   //                       border: Border.all(
//   //                         color: Theme.of(context).customColors.primaryColor!,
//   //                         width: 1.5,
//   //                       ),
//   //                     ),
//   //                     child: Icon(
//   //                       Icons.add_a_photo,
//   //                       size: 32,
//   //                       color: Theme.of(context).customColors.primaryColor,
//   //                     ),
//   //                   ),
//   //                 ),
//   //               );
//   //             } else {
//   //               final imagePath = state.photoPaths[index - 1];
//   //               return Stack(
//   //                 children: [
//   //                   CustomImageView(
//   //                     height: 90.h,
//   //                     width: 80.w,
//   //                     imagePath: imagePath,
//   //                     radius: BorderRadius.circular(12.r),
//   //                   ),
//   //                   Positioned(
//   //                     top: 2,
//   //                     right: 2,
//   //                     child: GestureDetector(
//   //                       onTap: () {
//   //                         context.read<ProfileBloc>().add(
//   //                           RemovePhoto(imagePath),
//   //                         );
//   //                       },
//   //                       child: Container(
//   //                         decoration: BoxDecoration(
//   //                           color: Theme.of(context).customColors.fillColor,
//   //                           shape: BoxShape.circle,
//   //                         ),
//   //                         child: Icon(
//   //                           Icons.close,
//   //                           size: 18,
//   //                           color: Theme.of(context).customColors.redcolor,
//   //                         ),
//   //                       ),
//   //                     ),
//   //                   ),
//   //                 ],
//   //               );
//   //             }
//   //           },
//   //         ),
//   //       ),
//   //     ],
//   //   );
//   // }
// }
